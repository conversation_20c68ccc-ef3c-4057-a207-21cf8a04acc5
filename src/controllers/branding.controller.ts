import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {repository} from '@loopback/repository';
import {api, post, requestBody, get, param, Response, RestBindings, Request, del, patch, HttpErrors} from '@loopback/rest';
import {guardStrategy, GuardSkipStrategy, skipGuardCheck, injectUserOrgId} from '../interceptors';
import {CurrencyRepository, ImageRepository, OnboardingStateRepository, OnboardingTaskRepository, OrganizationRepository} from '../repositories';
import {OnboardingTaskStateService, basicAuthorization, OpenAiService} from '../services';
import {Organization} from '../models';
import {ShopifyApiInvoker} from '../services/shopify/shopify-api-invoker.service';
import {service, inject} from '@loopback/core';
import {SecurityBindings, UserProfile as User} from '@loopback/security';
import {uuid} from 'uuidv4';
import * as Utils from '../utils/utils';
const multer = require('multer')
import * as AWS from 'aws-sdk';
import {convertCurrencyPlaceholdersToValues, convertCurrencyValuesToPlaceholders} from './admin-ui.controller';
import {Image} from '../models';
const upload = multer()


@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class BrandingController {
	constructor(
		@repository('ImageRepository')
		private imageRepository: ImageRepository,
		@repository(OrganizationRepository)
		private orgRepository: OrganizationRepository,
		@service(ShopifyApiInvoker)
		private shopifyApiInvoker: ShopifyApiInvoker,
		@repository(OnboardingStateRepository)
		private onboardingStateRepository: OnboardingStateRepository,
		@repository(OnboardingTaskRepository)
		private onboardingTaskRepository: OnboardingTaskRepository,
		@service(OnboardingTaskStateService)
		public onboardingTaskStateService: OnboardingTaskStateService,
		@repository(CurrencyRepository)
		private currencyRepository: CurrencyRepository,
	) {
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/branding')
	@skipGuardCheck()
	async saveBranding(
		@requestBody() branding: Branding,
		@param.query.string('flavor') flavor: 'referrals' | 'notifications' | 'checkout' | 'standard' | undefined,
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		if (branding && branding.custom && typeof branding.custom === 'string') {
			branding.custom = JSON.parse(branding.custom);
		}

		const stringsToConvert: {[key: string]: string} = {};

		if (flavor === 'referrals' && branding.referrals) {
			stringsToConvert['referralsTitle'] = branding.referrals.title;
			stringsToConvert['referralsCta'] = branding.referrals.callToAction;
		}

		const results = Object.keys(stringsToConvert).length ? await convertCurrencyValuesToPlaceholders(stringsToConvert) : {};


		if (flavor === 'referrals' && branding.referrals) {
			const {referralsTitle, referralsCta} = results;

			branding.referrals.title = referralsTitle;
			branding.referrals.callToAction = referralsCta;
		}

		const orgUpdate = this.orgRepository.updateById(orgId, {
			branding: JSON.stringify(branding)
		});

		const shopUpdate = this.shopifyApiInvoker.invokeAdminApi(
			orgId,
			'/raleon-data/branding',
			'POST',
			JSON.stringify({
				branding,
			})
		);

		await Promise.all([orgUpdate, shopUpdate]);


		this.onboardingTaskStateService.updateState({ state: 'Verified' }, 'Branding', orgId).catch();
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@get('/branding')
	@skipGuardCheck()
	async getBranding(
		@injectUserOrgId() orgId: number
	): Promise<any> {
		const org = await this.orgRepository.findById(orgId);
		const branding: Branding = org.branding ? JSON.parse(org.branding) : {};


		const orgCurrency = await this.currencyRepository.findOne({
			where: {
				organizationId: orgId
			},
			include: [
				{
					relation: 'supportedCurrencies'
				}
			]
		});

		if (branding.referrals) {
			branding.referrals.title = convertCurrencyPlaceholdersToValues(branding.referrals.title, orgCurrency!);
			branding.referrals.callToAction = convertCurrencyPlaceholdersToValues(branding.referrals.callToAction, orgCurrency!);
		}


		return branding;
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/branding/image/upload', {
		responses: {
			'200': {
				description: 'URI of uploaded image',
				content: {
					'text/plain': {},
				},
			},
		},
	})
	async uploadStandardImage(
		@injectUserOrgId() orgId: number,
		@requestBody.file()
		@inject(RestBindings.Http.RESPONSE) response: Response,
		@inject(RestBindings.Http.REQUEST) request: Request,
	): Promise<string> {
		const storage = multer.memoryStorage();
		const upload = multer({storage});

		return new Promise<string>((resolve, reject) => {
			try {
				upload.single('file')(request, response, async (err: any) => {
					try {
						if (err) return reject(err);

						const image = (request as any).file.buffer;
						const filename = (request as any).file.originalname;
						const width = request.query.width ? Number(request.query.width) : undefined;
						const height = request.query.height ? Number(request.query.height) : undefined;
						const imageType = request.query.imageType as string | undefined;
						const assetType = request.query.assetType as string | undefined;
						const description = request.query.description as string | undefined;
						const friendlyname = request.query.name ? request.query.name as string : filename;
						const isTemporary = request.query.temp === 'true';

						// Generate unique filename for temporary uploads to prevent conflicts
						const finalFilename = isTemporary
							? `temp_${Date.now()}_${filename}`
							: filename;

						// Upload to S3
						await this.uploadImageToBucket(finalFilename, orgId, image, isTemporary);

						// Get public URL
						let publicURL = this.getImageURL(finalFilename, orgId, isTemporary);

						// Only store in database if not temporary
						if (!isTemporary) {
							await this.imageRepository.create({
								friendlyname,
								url: publicURL,
								orgId,
								width,
								height,
								imageType,
								assetType,
								description
							});
						}

						resolve(publicURL);
					} catch (e2) {
						console.error(e2);
						reject(e2);
					}
				});
			} catch (e) {
				console.error(e);
				reject(e);
			}
		});
	}

	async uploadImageToBucket(filename: string, orgId: number, imageBuffer: Buffer, isTemporary = false) {
		if (imageBuffer.length > 30 * 1024 * 1024) {
			return Promise.reject('Image too large');
		}

		const s3 = new AWS.S3({
			accessKeyId: process.env.AWS_ACCESS_KEY,
			secretAccessKey: process.env.AWS_SECRET_KEY,
			region: 'us-east-1'
		});

		// Use a different path for temporary files
		const key = isTemporary
			? `temp/organization=${orgId}/${filename}`
			: `images/organization=${orgId}/${filename}`;

		try {
			console.log(`Uploading ${isTemporary ? 'temporary' : 'permanent'} image to S3: ${key}`);
			await s3.putObject({
				Bucket: 'loyalty-image-gallery',
				Key: key,
				Body: imageBuffer,
				ContentType: 'image/png',
				ACL: 'public-read',
				// Add expiration for temporary files (24 hours)
				...(isTemporary && {
					Expires: new Date(Date.now() + 24 * 60 * 60 * 1000)
				})
			}).promise();
		} catch (err) {
			console.error(err);
		}
	}

	getImageURL(filename: string, orgId: number, isTemporary = false): string {
		// Use appropriate path based on whether file is temporary
		const path = isTemporary
			? `temp/organization=${orgId}/${encodeURIComponent(filename)}`
			: `images/organization=${orgId}/${encodeURIComponent(filename)}`;

		// Use the CloudFront CDN for faster delivery
		const s3Domain = 'dqpqjbq51w8fz.cloudfront.net';
		return `https://${s3Domain}/${path}`;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/branding/images/{id}', {
		responses: {
			'200': {
				description: 'Image',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': Image,
						},
					},
				},
			},
		},
	})
	async getImageById(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<Image> {
		const image = await this.imageRepository.findOne({
			where: {
				id: id,
				orgId: orgId
			}
		});
		if (!image) {
			throw new HttpErrors.NotFound('Image not found');
		}
		return image;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/branding/images', {
		responses: {
			'200': {
				description: 'Array of Images',
				content: {
					'application/json': {
						schema: {
							type: 'array',
							items: {
								'x-ts-type': Image,
							},
						},
					},
				},
			},
		},
	})
	async getImages(
		@param.query.string('assetType') assetType: string,
		@injectUserOrgId() orgId: number,
	): Promise<Image[]> {
		let filter: any = {
			where: {
				orgId: orgId
			}
		};

		if (assetType) {
			filter.where.assetType = assetType;
		}

		return this.imageRepository.find(filter);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/branding/images/paginated', {
		responses: {
			'200': {
				description: 'Paginated images with metadata',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								images: {
									type: 'array',
									items: {
										type: 'object',
										properties: {
											url: { type: 'string' },
											width: { type: 'number' },
											height: { type: 'number' }
										}
									}
								},
								hasMore: { type: 'boolean' },
								total: { type: 'number' }
							}
						},
					},
				},
			},
		},
	})
	async getPaginatedImages(
		@param.query.string('assetType') assetType: string,
		@param.query.number('page') page: number = 1,
		@param.query.number('perPage') perPage: number = 20,
		@injectUserOrgId() orgId: number,
	): Promise<{images: any[], hasMore: boolean, total: number}> {
		// Calculate skip for pagination
		const skip = (page - 1) * perPage;

		// Create filter
		let filter: any = {
			where: {
				orgId: orgId
			},
			limit: perPage,
			skip: skip
		};

		if (assetType) {
			filter.where.assetType = assetType;
		}

		// Get total count for pagination
		const total = await this.imageRepository.count(filter.where);

		// Get images for current page
		const images = await this.imageRepository.find(filter);

		// Map images to required format
		const formattedImages = images.map(image => {
			return {
				url: image.url,
				width: image.width || 0,
				height: image.height || 0,
				contentType: image.contentType || image.imageType || 'image/jpeg',
				// Additional properties could be included here if needed
				id: image.id,
				friendlyname: image.friendlyname
			};
		});

		// Check if there are more images to load
		const hasMore = total.count > (page * perPage);

		// Return formatted response
		return {
			images: formattedImages,
			hasMore,
			total: total.count
		};
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/branding/images/metadata', {
		responses: {
			'200': {
				description: 'Image metadata saved successfully',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': Image,
						},
					},
				},
			},
		},
	})
	async saveImageMetadata(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['url'],
						properties: {
							url: {
								type: 'string',
							},
							width: {
								type: 'number',
							},
							height: {
								type: 'number',
							},
							contentType: {
								type: 'string',
							},
							friendlyname: {
								type: 'string',
							},
							assetType: {
								type: 'string',
							},
							imageType: {
								type: 'string',
							},
							description: {
								type: 'string',
							},
						},
					},
				},
			},
		})
		imageData: {
			url: string;
			width?: number;
			height?: number;
			contentType?: string;
			friendlyname?: string;
			assetType?: string;
			imageType?: string;
			description?: string;
		},
		@injectUserOrgId() orgId: number,
	): Promise<Image> {
		// Create the image record with just the essential metadata
		const image = await this.imageRepository.create({
			url: imageData.url,
			width: imageData.width,
			height: imageData.height,
			contentType: imageData.contentType,
			assetType: imageData.assetType,
			imageType: imageData.imageType,
			description: imageData.description,
			// Use the URL filename as friendlyname if not provided
			friendlyname: imageData.friendlyname || imageData.url.split('/').pop() || 'image',
			orgId: orgId
		});

		return image;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@patch('/branding/images/{id}', {
		responses: {
			'204': {
				description: 'Image PATCH success',
			},
		},
	})
	async updateImage(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							friendlyname: { type: 'string' },
							imageType: { type: 'string' },
							width: { type: 'number' },
							height: { type: 'number' },
							description: { type: 'string' },
							assetType: { type: 'string' }
						}
					}
				},
			},
		})
		image: Partial<Image>,
		@injectUserOrgId() orgId: number,
	): Promise<void> {
		// First, find the image to ensure it belongs to the organization
		const existingImage = await this.imageRepository.findOne({
			where: {
				id: id,
				orgId: orgId
			}
		});

		if (!existingImage) {
			throw new HttpErrors.NotFound('Image not found or does not belong to your organization');
		}

		// Update the image
		await this.imageRepository.updateById(id, image);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@del('/branding/images/{id}', {
		responses: {
			'204': {
				description: 'Image DELETE success',
			},
		},
	})
	async deleteImage(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number
	): Promise<void> {
		// First, find the image to ensure it belongs to the organization
		const image = await this.imageRepository.findOne({
			where: {
				id: id,
				orgId: orgId
			}
		});

		if (!image) {
			throw new Error('Image not found or does not belong to your organization');
		}

		// Delete the image from S3 - DONT DELETE FROM S3 so URLs dont break
		// const s3 = new AWS.S3({
		// 	accessKeyId: process.env.AWS_ACCESS_KEY,
		// 	secretAccessKey: process.env.AWS_SECRET_KEY,
		// 	region: 'us-east-1'
		// });

		// try {
		// 	// Extract filename from URL
		// 	const url = image.url;
		// 	const filename = decodeURIComponent(url.substring(url.lastIndexOf('/') + 1));

		// 	await s3.deleteObject({
		// 		Bucket: 'loyalty-image-gallery',
		// 		Key: `images/organization=${orgId}/${filename}`
		// 	}).promise();
		// } catch (err) {
		// 	console.error('Error deleting image from S3:', err);
		// 	// Continue with deletion from database even if S3 deletion fails
		// }

		// Delete from database
		await this.imageRepository.deleteById(id);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/branding/images/generate-description', {
		responses: {
			'200': {
				description: 'Generate AI description for an image',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								description: { type: 'string' }
							}
						}
					}
				}
			}
		}
	})
	async generateImageDescription(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['imageUrl'],
						properties: {
							imageUrl: { type: 'string' },
							imageType: { type: 'string' }
						}
					}
				}
			}
		})
		data: {
			imageUrl: string;
			imageType?: string;
		},
		@injectUserOrgId() orgId: number,
		@inject(SecurityBindings.USER) user: User,
		@service(OpenAiService) openAiService: OpenAiService,
	): Promise<{description: string}> {
		// Construct prompt for AI
		const prompt = `I need to analyze an image and generate a detailed description of it.

Please provide a description that:
1. Describes the key visual elements, style, and overall composition, what is in this image.
	Include descriptions of the people, objects, and any other details in the image.
2. Specifies whether it works best on light or dark backgrounds
3. Notes any color or contrast requirements
4. Identifies any limitations or specific cases where the image shouldn't be used
5. THE image cannot be modified in any way. It must be used as is. So do not recommend any changes to the image.
6. Keep your response to less than 3 sentences.

Include the following int he description:
Main Text in the image or N/A
Sub Text in the image or N/A
Button Text in the image or N/A

All of the description and the text should just be in the description field.


Format the response as JSON with a single "description" field containing the formatted text along with the text fields.
Make descriptions direct and concise, avoiding phrases like "The image shows" or "This image contains".`;

		try {
			const result = await openAiService.getImageDescription(prompt, data.imageUrl);
			const parsed = JSON.parse(result);
			return { description: parsed.description };
		} catch (error) {
			console.error('Error generating image description:', error);
			throw new HttpErrors.InternalServerError('Failed to generate image description');
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/branding/fonts/upload', {
		responses: {
			'200': {
				description: 'URI of uploaded font',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								url: { type: 'string' },
								filename: { type: 'string' }
							}
						}
					},
				},
			},
		},
	})
	async uploadFont(
		@injectUserOrgId() orgId: number,
		@requestBody.file()
		@inject(RestBindings.Http.RESPONSE) response: Response,
		@inject(RestBindings.Http.REQUEST) request: Request,
	): Promise<{url: string, filename: string}> {
		const storage = multer.memoryStorage();
		const upload = multer({storage});

		return new Promise<{url: string, filename: string}>((resolve, reject) => {
			try {
				upload.single('file')(request, response, async (err: any) => {
					try {
						if (err) return reject(err);

						const fontBuffer = (request as any).file.buffer;
						const filename = (request as any).file.originalname;
						const weight = request.query.weight as string || 'regular';
						const fontFamily = request.query.fontFamily as string || 'Custom Font';

						// Validate file type
						const allowedExtensions = ['.ttf', '.otf', '.woff', '.woff2'];
						const fileExtension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
						if (!allowedExtensions.includes(fileExtension)) {
							return reject(new HttpErrors.BadRequest('Invalid font file type. Only TTF, OTF, WOFF, and WOFF2 files are supported.'));
						}

						// Validate file size (max 5MB for fonts)
						if (fontBuffer.length > 5 * 1024 * 1024) {
							return reject(new HttpErrors.BadRequest('Font file too large. Maximum size is 5MB.'));
						}

						// Generate unique filename to prevent conflicts
						const timestamp = Date.now();
						const finalFilename = `${fontFamily.toLowerCase().replace(/\s+/g, '-')}_${weight}_${timestamp}${fileExtension}`;

						// Upload to S3
						await this.uploadFontToBucket(finalFilename, orgId, fontBuffer);

						// Get public URL
						const publicURL = this.getFontURL(finalFilename, orgId);

						resolve({
							url: publicURL,
							filename: finalFilename
						});
					} catch (e2) {
						console.error('Font upload error:', e2);
						reject(e2);
					}
				});
			} catch (e) {
				console.error('Font upload setup error:', e);
				reject(e);
			}
		});
	}

	async uploadFontToBucket(filename: string, orgId: number, fontBuffer: Buffer) {
		if (fontBuffer.length > 5 * 1024 * 1024) {
			return Promise.reject('Font file too large');
		}

		const s3 = new AWS.S3({
			accessKeyId: process.env.AWS_ACCESS_KEY,
			secretAccessKey: process.env.AWS_SECRET_KEY,
			region: 'us-east-1'
		});

		const key = `fonts/organization=${orgId}/${filename}`;

		// Determine content type based on file extension
		const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
		let contentType = 'application/octet-stream';
		switch (extension) {
			case '.ttf':
				contentType = 'font/ttf';
				break;
			case '.otf':
				contentType = 'font/otf';
				break;
			case '.woff':
				contentType = 'font/woff';
				break;
			case '.woff2':
				contentType = 'font/woff2';
				break;
		}

		try {
			console.log(`Uploading font to S3: ${key}`);
			await s3.putObject({
				Bucket: 'loyalty-image-gallery', // Using same bucket as images
				Key: key,
				Body: fontBuffer,
				ContentType: contentType,
				ACL: 'public-read',
				// Set appropriate cache headers for fonts
				CacheControl: 'public, max-age=31536000', // 1 year cache
			}).promise();
		} catch (err) {
			console.error('S3 font upload error:', err);
			throw err;
		}
	}

	getFontURL(filename: string, orgId: number): string {
		const path = `fonts/organization=${orgId}/${encodeURIComponent(filename)}`;
		// Use the same CloudFront CDN as images
		const s3Domain = 'dqpqjbq51w8fz.cloudfront.net';
		return `https://${s3Domain}/${path}`;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/branding/fonts/generate-css', {
		responses: {
			'200': {
				description: 'Generated CSS URL for custom fonts',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								cssUrl: { type: 'string' },
								fontFamily: { type: 'string' }
							}
						}
					},
				},
			},
		},
	})
	async generateFontCSS(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['fontFamily', 'fonts'],
						properties: {
							fontFamily: {
								type: 'string',
								description: 'The font family name'
							},
							fonts: {
								type: 'object',
								description: 'Object with weight as key and font URL as value (e.g., {"400": "url1", "700": "url2"})',
								additionalProperties: {
									type: 'string'
								}
							}
						}
					}
				}
			}
		})
		fontData: {
			fontFamily: string;
			fonts: {
				[weight: string]: string; // Weight as key, URL as value
			};
		},
		@injectUserOrgId() orgId: number,
	): Promise<{cssUrl: string, fontFamily: string}> {
		try {
			// Generate CSS content
			let cssContent = '';

			// Generate @font-face rules for each weight
			Object.entries(fontData.fonts).forEach(([weight, url]) => {
				// Determine font format from URL extension
				let format = 'truetype'; // default
				if (url.includes('.woff2')) {
					format = 'woff2';
				} else if (url.includes('.woff')) {
					format = 'woff';
				} else if (url.includes('.otf')) {
					format = 'opentype';
				}

				cssContent += `@font-face {
	font-family: '${fontData.fontFamily}';
	src: url('${url}') format('${format}');
	font-weight: ${weight};
	font-style: normal;
	font-display: swap;
}

`;
			});

			// Generate filename for CSS
			const timestamp = Date.now();
			const cssFilename = `${fontData.fontFamily.toLowerCase().replace(/\s+/g, '-')}_${timestamp}.css`;

			// Upload CSS to S3
			const s3 = new AWS.S3({
				accessKeyId: process.env.AWS_ACCESS_KEY,
				secretAccessKey: process.env.AWS_SECRET_KEY,
				region: 'us-east-1'
			});

			const key = `fonts/css/organization=${orgId}/${cssFilename}`;

			await s3.putObject({
				Bucket: 'loyalty-image-gallery',
				Key: key,
				Body: cssContent,
				ContentType: 'text/css',
				ACL: 'public-read',
				CacheControl: 'public, max-age=31536000', // 1 year cache
			}).promise();

			// Generate public URL for CSS
			const s3Domain = 'dqpqjbq51w8fz.cloudfront.net';
			const cssUrl = `https://${s3Domain}/${key}`;

			console.log(`Generated CSS for font family "${fontData.fontFamily}" at: ${cssUrl}`);

			return {
				cssUrl,
				fontFamily: fontData.fontFamily
			};

		} catch (error) {
			console.error('Error generating font CSS:', error);
			throw new HttpErrors.InternalServerError('Failed to generate font CSS');
		}
	}
}



interface AccentColor {
	from: string;
	to: string;
}

interface Colors {
	useBackgroundColor: boolean;
	backgroundColor: string;
	textColor: string;
	buttonTextColor: string;
	buttonBackgroundColor: string;
	linkColor: string;
	accentColor: AccentColor;
	secondaryColor: string;
	warningColor: string;
}

interface LauncherStyling {
	textColor: string;
	backgroundColor: string;
}

interface Launcher {
	logoUrl: string;
	launcherPosition: { left: string; right: string };
	callToAction: string;
	styling: LauncherStyling;
	size: string;
	radius: string;
}

interface GuestContent {
	title: string;
	subtitle: string;
	benefitsTitle: string;
	benefitsList: string[];
	benefitsBackgroundColor: string;
}

interface Guest {
	heroImageUrl: string;
	content: GuestContent;
}

interface MemberContent {
	rewardsTitle: string;
	wteTitle: string;
	rewardShopTitle: string;
	perksTitle?: string;
}

interface Member {
	content: MemberContent;
	enableHistory: boolean;
}

interface Referrals {
	title: string;
	callToAction: string;
	styling: {
		textColor: string;
		fillBorderColor: string;
		titleFontWeight: string;
		descriptionFontWeight: string;
		buttonMaxWidth: string;
		buttonFontSize: string;
		inputFontSize: string;
		inputWidth: string;
		buttonFontWeight: string;
		titleFontSize: string;
		descriptionFontSize: string;
	},
	shareSettings?: {
		facebook?: {
			enabled: boolean;
			text: string;
		},
		twitter?: {
			enabled: boolean;
			text: string;
		},
		email?: {
			enabled: boolean;
			text: string;
			showEmailInput: boolean;
			subject: string;
		},
		whatsapp?: {
			enabled: boolean;
			text: string;
		},
		instagram?: {
			enabled: boolean;
			text: string;
		},
		fbmessenger?: {
			enabled: boolean;
			text: string;
		},
		sms?: {
			enabled: boolean;
			text: string;
		},
		tiktok?: {
			enabled: boolean;
			text: string;
		},
		linkedin?: {
			enabled: boolean;
			text: string;
		},
		pinterest?: {
			enabled: boolean;
			text: string;
		},
	}
}

interface Branding {
	header: string;
	logoUrl: string;
	colors: Colors;
	launcher: Launcher;
	guest: Guest;
	member: Member;
	referrals: Referrals;
	notifications?: {
		styling?: {
			backgroundColor: string;
			textColor: string;
			fillBorderColor: string;
		}
	}
	custom: any;
}
