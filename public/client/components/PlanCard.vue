<template>
	<div
		class="relative bg-white border-2 rounded-2xl p-8 transition-all duration-300 hover:shadow-xl overflow-hidden flex flex-col h-full"
		:class="[
			isFeatured ? 'border-indigo-500 shadow-lg' : 'border-gray-200 shadow-md',
			'hover:border-indigo-300'
		]"
	>
		<!-- Sheen Animation for Featured Cards -->
		<div
			v-if="isFeatured"
			class="absolute top-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/40 to-transparent pointer-events-none z-10 transition-none"
			:style="{ left: `${sheenPosition}%` }"
		></div>

		<!-- Popular Badge -->
		<div
			v-if="isFeatured"
			class="absolute -top-0.5 right-5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-1.5 rounded-b-lg text-xs font-bold uppercase tracking-wide"
		>
			Most Popular
		</div>

		<!-- Plan Header - Fixed Height with more space -->
		<div class="text-center mb-6 h-36 flex flex-col justify-center pt-6">
			<h3 class="text-2xl font-bold text-gray-900 mb-2">{{ planName }}</h3>
			<p class="text-gray-600 text-sm leading-relaxed px-2">{{ description }}</p>
		</div>

		<!-- Revenue Selector Section - Fixed Height for Alignment -->
		<div class="mb-6 h-24 flex flex-col justify-center pt-4">
			<!-- Revenue Dropdown for revenue-based plans -->
			<div v-if="hasRevenuePricing && revenueOptions.length > 0" class="text-center">
				<label class="text-sm font-medium text-gray-700 block mb-2">Yearly Online Sales</label>
				<div class="relative">
					<select
						:value="selectedRevenue"
						@change="$emit('revenue-change', $event.target.value)"
						class="w-full px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium bg-white shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 cursor-pointer appearance-none"
					>
						<option v-for="option in revenueOptions" :key="option.value" :value="option.value">
							{{ option.label }}
						</option>
					</select>
					<div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
						<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
						</svg>
					</div>
				</div>
			</div>

			<!-- No Sales Limit for non-revenue-based plans -->
			<div v-else class="text-center">
				<label class="text-sm font-medium text-gray-700 block mb-2">Yearly Online Sales</label>
				<div class="w-full px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium bg-gray-50 text-gray-600 flex items-center justify-center">
					<span>No sales limit</span>
				</div>
			</div>
		</div>

		<!-- Pricing Section - Fixed Height for Alignment -->
		<div class="text-center mb-8 h-24 flex flex-col justify-center">
			<div class="mb-2 flex items-baseline justify-center">
				<div v-if="price === 'Custom'" class="text-4xl font-bold text-gray-900 leading-tight font-mono">
					Custom
				</div>
				<div v-else class="text-4xl font-bold text-gray-900 leading-tight font-mono">
					${{ typeof price === 'number' ? price.toLocaleString() : price }}<span class="text-lg font-normal text-gray-600 font-sans">/{{ period }}</span>
				</div>
			</div>
			<div class="text-sm text-gray-500">Billed monthly</div>
		</div>

		<!-- CTA Button Area - Centered between price and features -->
		<div class="mb-8">
			<!-- Contact Sales Button -->
			<button
				v-if="isContactSales"
				@click="$emit('contact-sales')"
				class="w-full py-3 px-6 rounded-xl font-semibold text-white transition-all duration-300 transform hover:-translate-y-1"
				:class="isFeatured
					? 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
					: 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-md hover:shadow-lg'"
			>
				Contact Sales
			</button>

			<!-- Regular Plan Buttons -->
			<template v-else>
				<!-- Get Started / Reactivate -->
				<button
					v-if="!isActive && !isPendingCancellation && !isPendingActivation && !isPendingDowngrade"
					@click="$emit('choose-plan')"
					class="w-full py-3 px-6 rounded-xl font-semibold text-white transition-all duration-300 transform hover:-translate-y-1"
					:class="isFeatured
						? 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
						: 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-md hover:shadow-lg'"
				>
					Get Started
				</button>

				<!-- Reactivate Plan -->
				<button
					v-else-if="(isPendingCancellation || isPendingDowngrade) && !isActive"
					@click="$emit('choose-plan')"
					class="w-full py-3 px-6 rounded-xl font-semibold text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg"
				>
					Reactivate Plan
				</button>

				<!-- Current Plan (Trial) -->
				<button
					v-else-if="isActive && isInTrial"
					@click="$emit('choose-plan')"
					class="w-full py-3 px-6 rounded-xl font-semibold text-green-800 bg-green-100 border-2 border-green-200"
				>
					Get Started
				</button>

				<!-- Current Plan (Active) -->
				<button
					v-else-if="isActive && !isPendingCancellation && !isPendingDowngrade && !isPending"
					disabled
					class="w-full py-3 px-6 rounded-xl font-semibold text-green-800 bg-green-100 border-2 border-green-200"
				>
					Current Plan
				</button>

				<!-- Pending Plan (Awaiting Payment) -->
				<button
					v-else-if="isPending"
					disabled
					class="w-full py-3 px-6 rounded-xl font-semibold text-yellow-800 bg-yellow-100 border-2 border-yellow-200"
				>
					Awaiting Payment
				</button>

				<!-- Pending Cancellation -->
				<button
					v-else-if="isActive && isPendingCancellation"
					disabled
					class="w-full py-3 px-6 rounded-xl font-semibold text-orange-800 bg-orange-100 border-2 border-orange-200"
				>
					Cancelling {{ pendingCancellationDate }}
				</button>

				<!-- Pending Downgrade -->
				<button
					v-else-if="isActive && isPendingDowngrade"
					disabled
					class="w-full py-3 px-6 rounded-xl font-semibold text-blue-800 bg-blue-100 border-2 border-blue-200"
				>
					Downgrading to {{ downgradeTarget }}
				</button>

				<!-- Pending Activation -->
				<button
					v-else-if="isPendingActivation"
					disabled
					class="w-full py-3 px-6 rounded-xl font-semibold text-green-800 bg-green-100 border-2 border-green-200"
				>
					Activating {{ pendingActivationDate }}
				</button>

				<!-- Cancel Button -->
				<button
					v-if="isActive && !isInTrial && !isPendingCancellation && !isPendingDowngrade"
					@click="$emit('unsubscribe-plan')"
					class="w-full mt-3 py-2 px-6 rounded-xl font-medium text-gray-700 border-2 border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-300"
				>
					Cancel
				</button>

				<!-- Downgrade Scenario Buttons -->
				<template v-if="isActive && isPendingDowngrade">
					<button
						@click="$emit('choose-plan')"
						class="w-full mt-3 py-2 px-6 rounded-xl font-medium text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 transition-all duration-300"
					>
						Keep This Plan
					</button>
					<button
						@click="$emit('unsubscribe-plan')"
						class="w-full mt-2 py-2 px-6 rounded-xl font-medium text-gray-700 border-2 border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-300"
					>
						Cancel All Plans
					</button>
				</template>
			</template>
		</div>

		<!-- Features List - Flexible Height -->
		<div class="flex-grow">
			<div v-if="includesText" class="text-sm font-semibold text-indigo-600 text-center mb-4">
				{{ includesText }}
			</div>

			<ul class="space-y-3">
				<li v-for="(feature, index) in features" :key="index" class="flex items-start gap-3">
					<div class="flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
						<svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
						</svg>
					</div>
					<div class="flex-1 flex items-center justify-between">
						<span class="text-gray-700 text-sm">{{ feature.text }}</span>
						<div
							v-if="feature.tooltip"
							class="relative"
							@mouseenter="activeTooltip = index"
							@mouseleave="activeTooltip = null"
						>
							<div class="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center cursor-help">
								<span class="text-white text-xs font-bold">i</span>
							</div>
							<div
								v-if="activeTooltip === index"
								class="absolute bottom-full right-0 mb-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-50"
							>
								{{ feature.tooltip }}
								<div class="absolute top-full right-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
							</div>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
</template>

<script>
export default {
	name: 'PlanCard',
	props: {
		planName: {
			type: String,
			required: true
		},
		price: {
			type: [Number, String],
			required: true
		},
		period: {
			type: String,
			default: 'month'
		},
		description: {
			type: String,
			required: true
		},
		features: {
			type: Array,
			default: () => []
		},
		isFeatured: {
			type: Boolean,
			default: false
		},
		isActive: {
			type: Boolean,
			default: false
		},
		isPendingCancellation: {
			type: Boolean,
			default: false
		},
		isPendingDowngrade: {
			type: Boolean,
			default: false
		},
		isPendingActivation: {
			type: Boolean,
			default: false
		},
		isPending: {
			type: Boolean,
			default: false
		},
		pendingCancellationDate: {
			type: String,
			default: null
		},
		pendingActivationDate: {
			type: String,
			default: null
		},
		downgradeTarget: {
			type: String,
			default: ''
		},
		isInTrial: {
			type: Boolean,
			default: false
		},
		isContactSales: {
			type: Boolean,
			default: false
		},
		includesText: {
			type: String,
			default: null
		},
		hasRevenuePricing: {
			type: Boolean,
			default: false
		},
		revenueOptions: {
			type: Array,
			default: () => []
		},
		selectedRevenue: {
			type: [String, Number],
			default: null
		}
	},
	data() {
		return {
			sheenPosition: -100,
			activeTooltip: null
		}
	},
	mounted() {
		if (this.isFeatured) {
			this.startSheenAnimation();
		}
	},
	methods: {
		startSheenAnimation() {
			const animateSheen = () => {
				this.sheenPosition = -100;
				const duration = 1000;
				const startTime = Date.now();

				const animate = () => {
					const elapsed = Date.now() - startTime;
					const progress = Math.min(elapsed / duration, 1);
					const position = -100 + progress * 200;

					this.sheenPosition = position;

					if (progress < 1) {
						requestAnimationFrame(animate);
					}
				};

				requestAnimationFrame(animate);
			};

			// Initial delay, then repeat every 3 seconds
			setTimeout(() => {
				animateSheen();
				setInterval(animateSheen, 3000);
			}, 1000);
		}
	}
}
</script>
