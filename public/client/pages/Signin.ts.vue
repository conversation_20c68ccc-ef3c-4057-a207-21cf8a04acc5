<template>
	<main class="bg-white">
		<div v-if="intialized" class="relative flex">
			<!-- Content -->
			<div class="w-full md:w-1/2">
				<div class="min-h-screen h-full flex flex-col after:flex-1 z-50">
					<div class="flex-1">
						<div class="
	                flex
	                items-center
	                justify-between
	                h-16
	                px-4
	                sm:px-6
	                lg:px-8
	              ">
							<!-- Logo -->
							<router-link class="block" to="/">
								<img src="../images/raleon-logo-whitebg.jpg" width="64" height="64" />
							</router-link>
						</div>
					</div>
					<div class="max-w-sm mx-auto px-4 py-8">
						<h1 class="text-3xl text-slate-800 font-bold mb-6">
							Welcome to Raleon, you rock! 🏆
						</h1>
						<!-- Form -->
						<div class="flex items-center p-4 mb-4 w-full text-gray-500 bg-red-100 border border-red-400 rounded-lg shadow"
							role="alert" v-if="loginError">
							<div
								class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-red-500 bg-red-100 rounded-lg text-red-700">
								<svg aria-hidden="true" class="w-5 h-5" fill="#f56565" viewBox="0 0 20 20"
									xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd"
										d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
										clip-rule="evenodd"></path>
								</svg>
								<span class="sr-only">Error icon</span>
							</div>
							<div class="ml-3 text-sm font-normal">Your username and/or password is incorrect.</div>
						</div>

						<form @submit.prevent="userLogin">
							<div class="space-y-4">
								<div>
									<label class="block text-sm font-medium mb-1" for="email">Email Address</label>
									<input id="email" class="
										form-input
										w-full
										focus:bg-gradient-to-r
										focus:from-ralpurple-500
										focus:to-ralocean-500
										" type="email" v-model="email" />
								</div>
								<div>
									<label class="block text-sm font-medium mb-1" for="password">Password</label>
									<input id="password" class="form-input w-full" type="password" autoComplete="on"
										v-model="password" />
								</div>
							</div>
							<div class="flex items-center justify-between mt-6">
								<button id="login-button" class="
									btn
									bg-indigo-500
									hover:bg-indigo-600
									text-white
									w-full
								" type="submit" @click.stop="userLogin">
									Sign In
								</button>
							</div>
							<div class="mt-3 text-center">
								<router-link class="text-sm underline hover:text-indigo-600" to="/reset-password">
									Forgot Password?
								</router-link>
							</div>
						</form>

						<!-- Divider -->
						<div class="flex items-center my-6">
							<div class="flex-1 border-t border-gray-300"></div>
							<div class="px-4 text-sm text-gray-500">or</div>
							<div class="flex-1 border-t border-gray-300"></div>
						</div>

						<!-- Sign up link -->
						<div class="text-center mb-4">
							<p class="text-sm text-gray-600">
								Don't have an account?
								<router-link class="font-medium text-indigo-600 hover:text-indigo-500" to="/signup">
									Sign up here
								</router-link>
							</p>
						</div>

						<!-- Footer -->
						<div class="pt-5 mt-3 border-t border-slate-200">
							<div class="space-y-3">
								<!-- Google Sign In -->
								<button
									@click="signInWithGoogle"
									class="btn bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 w-full inline-flex items-center justify-center space-x-2 py-2 px-4"
								>
									<svg class="w-5 h-5" viewBox="0 0 24 24">
										<path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
										<path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
										<path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
										<path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
									</svg>
									<span>Sign in with Google</span>
								</button>

								<!-- Shopify Sign In - Minimized -->
								<div class="text-center mt-4">
									<p class="text-xs text-gray-500 mb-2">For Shopify users:</p>
									<a
										class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 transition-colors"
										href="https://admin.shopify.com?redirect=/apps/raleon">
										<svg class="w-6 h-6 mr-1.5" viewBox="-18 0 292 292" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid">
											<path d="M223.774 57.34c-.201-1.46-1.48-2.268-2.537-2.357-1.055-.088-23.383-1.743-23.383-1.743s-15.507-15.395-17.209-17.099c-1.703-1.703-5.029-1.185-6.32-.805-.19.056-3.388 1.043-8.678 2.68-5.18-14.906-14.322-28.604-30.405-28.604-.444 0-.901.018-1.358.044C129.31 3.407 123.644.779 118.75.779c-37.465 0-55.364 46.835-60.976 70.635-14.558 4.511-24.9 7.718-26.221 8.133-8.126 2.549-8.383 2.805-9.45 10.462C21.3 95.806.038 260.235.038 260.235l165.678 31.042 89.77-19.42S223.973 58.8 223.775 57.34zM156.49 40.848l-14.019 4.339c.005-.988.01-1.96.01-3.023 0-9.264-1.286-16.723-3.349-22.636 8.287 1.04 13.806 10.469 17.358 21.32zm-27.638-19.483c2.304 5.773 3.802 14.058 3.802 25.238 0 .572-.005 1.095-.01 1.624-9.117 2.824-19.024 5.89-28.953 8.966 5.575-21.516 16.025-31.908 25.161-35.828zm-11.131-10.537c1.617 0 3.246.549 4.805 1.622-12.007 5.65-24.877 19.88-30.312 48.297l-22.886 7.088C75.694 46.16 90.81 10.828 117.72 10.828z" fill="#95BF46"/>
											<path d="M221.237 54.983c-1.055-.088-23.383-1.743-23.383-1.743s-15.507-15.395-17.209-17.099c-.637-.634-1.496-.959-2.394-1.099l-12.527 256.233 89.762-19.418S223.972 58.8 223.774 57.34c-.201-1.46-1.48-2.268-2.537-2.357" fill="#5E8E3E"/>
											<path d="M135.242 104.585l-11.069 32.926s-9.698-5.176-21.586-5.176c-17.428 0-18.305 10.937-18.305 13.693 0 15.038 39.2 20.8 39.2 56.024 0 27.713-17.577 45.558-41.277 45.558-28.44 0-42.984-17.7-42.984-17.7l7.615-25.16s14.95 12.835 27.565 12.835c8.243 0 11.596-6.49 11.596-11.232 0-19.616-32.16-20.491-32.16-52.724 0-27.129 19.472-53.382 58.778-53.382 15.145 0 22.627 4.338 22.627 4.338" fill="#FFF"/>
										</svg>
										Sign in via Shopify
									</a>
								</div>
                                                        </div>

						</div>
					</div>
				</div>
			</div>
			<div class="hidden md:block absolute top-0 bottom-0 right-0 md:w-1/2" aria-hidden="true">
				<img class="object-cover object-center w-full h-full" src="../images/r-login-bg.png" width="760"
					height="1024" alt="Authentication" />
			</div>
		</div>
	</main>
</template>

<script>

import * as Utils from '../utils/utils.js';
import * as userService from '../services/user.js'
import { useSessionStore } from './useSessionStore.ts';
const URL_DOMAIN = Utils.URL_DOMAIN;
const GOOGLE_CLIENT_ID = Utils.GOOGLE_CLIENT_ID;

export default {
	props: { redirect: String },
	name: 'Signin',
	data() {
		const sessionStore = useSessionStore();
		return {
			email: '',
			password: '',
			loginError: false,
			intialized: false,
			sessionStore,
		};
	},
	async mounted() {
		const sessionToken = this.$route.query?.session_id;
		const source = this.$route.query?.source;
		if(source == 'shopify_connect') {
			const onboardResponse = await fetch(`${URL_DOMAIN}/onboard/begin`, {
				method: 'POST',
				credentials: 'omit',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${localStorage.getItem('token')}`,
				},
				body: JSON.stringify({
				}),
			});
			if (!onboardResponse.ok) {
				console.log("Error starting onboarding process");
			}
			this.$router.push(`/integrations`);
		}
		if (sessionToken) {
			localStorage.removeItem('raleon_org_id');
			localStorage.removeItem('userInfo');
			localStorage.removeItem('userOrgId');
			localStorage.removeItem('token');

			const {token, shopifyRaleonSnippetId} = await userService.userLoginToken(sessionToken);

			localStorage.setItem('token', token);
			localStorage.setItem('shopify_snippet_id', shopifyRaleonSnippetId);
			let userInfo = await userService.getUserInfo();
			try {
				await userService.setUserInfoSignin(userInfo);
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}

			if (this.$route.query?.redirect == 'docsignin') {
				this.docLogin();
				this.intialized = true;
				this.sessionStore.logIn();
				return;
			}

			try {
				if (this.$route.query?.redirect) {
						let redirectPath = this.$route.query.redirect;
						if (!redirectPath.startsWith('/')) {
							redirectPath = `/${redirectPath}`;
						}
						this.sessionStore.logIn();
						this.$router.push(redirectPath);
				}
				else {
					this.sessionStore.logIn();
					if (source == 'shopify_install') {
						const onboardResponse = await fetch(`${URL_DOMAIN}/onboard/begin`, {
							method: 'POST',
							credentials: 'omit',
							mode: 'cors',
							headers: {
								'Content-Type': 'application/json',
								Authorization: `Bearer ${localStorage.getItem('token')}`,
							},
							body: JSON.stringify({
							}),
						});
						if (!onboardResponse.ok) {
							console.log("Error starting onboarding process");
						}
						const redirectPath = await this.getRedirectPath();
						this.$router.push(`${redirectPath}?onboarding=true`);
					} else {
						const redirectPath = await this.getRedirectPath();
						this.$router.push(redirectPath);
					}
					//this.$router.push(`/onboarding/start?sidebar=false&source=${source}`);
				}

				return;
			} catch(e) {
				console.log(`Error fetching loyalty programs ${e}`);
			}

		}

		let currentToken = localStorage.getItem('token');
		if (currentToken != undefined) {
			// lets verify this token is valid
			let userInfo = await userService.getUserInfo();
			if (
				(userInfo != undefined && userInfo.error == true) ||
				userInfo.error != undefined
			) {
				//We need to reset the token and stay here
				localStorage.removeItem('token');
			} else {
				//We are good to go
				if(this.redirect == 'docs') {
					this.sessionStore.logIn();
					this.docLogin();
				}
				else {
					this.sessionStore.logIn();
					const redirectPath = await this.getRedirectPath();
					this.$router.push(redirectPath);
				}
			}
		}
		this.intialized = true;
	},
	methods: {
		async getRedirectPath() {
			try {
				const token = localStorage.getItem('token');
				const orgId = localStorage.getItem('userOrgId');

				if (token && orgId) {
					// Fetch organization details to check if it's an agency
					const response = await fetch(`${URL_DOMAIN}/organizations/${orgId}`, {
						method: 'GET',
						headers: {
							Authorization: `Bearer ${token}`,
							'Content-Type': 'application/json',
						},
					});

					if (response.ok) {
						const orgData = await response.json();
						// Redirect agency organizations to /agency, others to /chat
						if (orgData.orgType === 'agency') {
							return '/agency';
						}
					}
				}
			} catch (error) {
				console.error('Error fetching organization details for redirect:', error);
			}

			// Default redirect to chat for brand organizations or if fetch fails
			return '/chat';
		},
		async docLogin() {
			let response = {};
			let jsonresponse = {};
			try {
				console.log("Trying request to /users/doc-login", `${URL_DOMAIN}/users/doc-login`)
				response = await fetch(`${URL_DOMAIN}/users/doc-login`, {
					method: 'GET',
					withCreditentials: true,
					credentials: 'omit',
					mode: 'cors',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
						'Access-Control-Allow-Origin': '*',
						'Content-Type': 'application/json',
						'ngrok-skip-browser-warning': true,
					}
				});
				console.log("Response from /users/doc-login", response)
				jsonresponse = await response.json();
				console.log("DOCS DOCS DOCS", jsonresponse)
				if(jsonresponse.docs_url)
				{
					this.dev_doc_url = `${jsonresponse.docs_url}`;
					window.location.replace(this.dev_doc_url);
				}

				console.log("DOCS DOCS DOCS", this.dev_doc_url);
			} catch (err) {
				console.log("Error: " + err);
			}
		},
                async userLogin() {
			console.log("userLogin")
			this.loginError = false;
			const loginResult = await userService.userLogin(
				this.email,
				this.password,
			);
			console.log(loginResult);
			if (loginResult.token) {
				localStorage.setItem('token', loginResult.token);
				localStorage.setItem('shopify_snippet_id', loginResult.shopifyRaleonSnippetId)
				let userInfo = await userService.getUserInfo();
				await userService.setUserInfoSignin(userInfo);
				if(this.redirect == 'docs') {
					this.docLogin();
				}
				else {
					this.sessionStore.logIn();
					const redirectPath = await this.getRedirectPath();
					this.$router.push(redirectPath);
				}
			} else {
				this.loginError = true;
			}
                },
                async signInWithGoogle() {
                        if (!window.google) {
                                await new Promise(resolve => {
                                        const script = document.createElement('script');
                                        script.src = 'https://accounts.google.com/gsi/client';
                                        script.onload = resolve;
                                        document.head.appendChild(script);
                                });
                        }

                        // Use OAuth2 with popup - larger modal with account selection
                        const tokenClient = window.google.accounts.oauth2.initTokenClient({
                                client_id: GOOGLE_CLIENT_ID,
                                scope: 'email profile',
                                hint: '',
                                hosted_domain: '',
								prompt: 'select_account',
                                callback: async (tokenResponse) => {
                                        if (tokenResponse && tokenResponse.access_token) {
                                                try {
                                                        // Get user info from Google
                                                        const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
                                                                headers: {
                                                                        'Authorization': `Bearer ${tokenResponse.access_token}`
                                                                }
                                                        });
                                                        const userInfo = await userResponse.json();

														console.log("Token:", tokenResponse);

                                                        // Send to your backend
                                                        const res = await fetch(`${URL_DOMAIN}/users/google-login`, {
                                                                method: 'POST',
                                                                headers: { 'Content-Type': 'application/json' },
                                                                body: JSON.stringify({
                                                                        email: userInfo.email,
                                                                        name: userInfo.name,
                                                                        access_token: tokenResponse.access_token
                                                                })
                                                        });

                                                        if (res.ok) {
                                                                const data = await res.json();
                                                                localStorage.setItem('token', data.token);
                                                                let userInfo = await userService.getUserInfo();
                                                                await userService.setUserInfoSignin(userInfo);
                                                                this.sessionStore.logIn();
                                                                const redirectPath = await this.getRedirectPath();
                                                                this.$router.push(redirectPath);
                                                        } else {
                                                                // Handle login failure
                                                                const errorData = await res.json().catch(() => ({}));
                                                                const errorMessage = errorData.message || 'Google sign-in failed';

                                                                if (res.status === 404) {
                                                                        this.loginError = 'User not found when signing in with Google. Please sign up first or use a different email.';
                                                                } else if (res.status === 401) {
                                                                        this.loginError = 'Google authentication failed. Please try again.';
                                                                } else {
                                                                        this.loginError = `Sign-in failed: ${errorMessage}`;
                                                                }
                                                        }
                                                } catch (error) {
                                                        console.error('Google sign-in error:', error);
                                                        this.loginError = 'An unexpected error occurred during Google sign-in. Please try again.';
                                                }
                                        }
                                }
                        });

                        // Request token with prompt for account selection
                        tokenClient.requestAccessToken({
                                prompt: 'select_account'
                        });
                },
                getCookie(name) {
			console.log(`cookies: ${document.cookie}`)
			const value = `; ${document.cookie}`;
			const parts = value.split(`; ${name}=`);
			if (parts.length === 2) return parts.pop().split(';').shift();
		}
	},
};
</script>
